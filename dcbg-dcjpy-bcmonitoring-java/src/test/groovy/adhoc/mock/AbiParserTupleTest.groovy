package adhoc.mock

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import org.web3j.abi.EventEncoder
import spock.lang.Specification

/**
 * Test class for AbiParser tuple handling with the updated createTypeReference method
 */
class AbiParserTupleTest extends Specification {

    def "test AbiParser can parse Transfer event with tuple from Token.json"() {
        given: "AbiParser instance"
        def properties = new BcmonitoringConfigurationProperties()
        def abiParser = new AbiParser(properties)

        and: "Token.json ABI content"
        def abiContent = this.class.getResourceAsStream("/abi_json/hardhat/3000/Token.json").text

        when: "Parsing Token.json ABI"
        def contractEvents = abiParser.parseAbi(abiContent)

        then: "The parsing should succeed"
        contractEvents != null

        and: "Transfer event should be found"
        def transferEvent = contractEvents.find { it.key == "Transfer" }
        transferEvent != null

        and: "Transfer event should be encodable"
        def eventSignature = EventEncoder.encode(transferEvent.value.getEvent())
        eventSignature != null
        eventSignature.startsWith("0x")

        and: "Event should have correct structure"
        def event = transferEvent.value.getEvent()
        event.getName() == "Transfer"
        event.getParameters().size() == 2 // tuple + traceId

        println("Transfer event signature from AbiParser: ${eventSignature}")
    }

    def "test createTypeReference handles tuple types correctly"() {
        given: "AbiParser instance"
        def properties = new BcmonitoringConfigurationProperties()
        def abiParser = new AbiParser(properties)

        when: "Creating TypeReference for tuple types"
        // This test verifies that the updated createTypeReference method works
        // The actual method is private, but we can test it indirectly through parseAbi
        def abiContent = this.class.getResourceAsStream("/abi_json/hardhat/3000/Token.json").text
        def contractEvents = abiParser.parseAbi(abiContent)

        then: "Parsing should succeed without errors"
        contractEvents != null
        contractEvents.size() > 0

        and: "Transfer event should be parseable"
        def transferEvent = contractEvents.find { it.key == "Transfer" }
        transferEvent != null

        println("Successfully parsed ${contractEvents.size()} events including Transfer")
    }
}
