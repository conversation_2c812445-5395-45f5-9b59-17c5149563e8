package adhoc.mock

import org.web3j.abi.EventEncoder
import spock.lang.Specification

/**
 * Test class for EventMockFactory to reproduce the TypeReference issue
 */
class EventMockFactoryTest extends Specification {

    def "test createMockTransferEvent should encode without errors"() {
        when: "Creating a mock Transfer event"
        def transferEvent = EventMockFactory.createMockTransferEvent()

        and: "Encoding the event"
        def eventSignature = EventEncoder.encode(transferEvent)

        then: "The encoding should succeed"
        eventSignature != null
        eventSignature.startsWith("0x")
        eventSignature.length() == 66 // 0x + 64 hex characters (32 bytes)

        and: "The event should have the correct name"
        transferEvent.getName() == "Transfer"

        and: "The event should have 2 parameters (tuple + traceId)"
        transferEvent.getParameters().size() == 2
    }

    def "test createMockTransferLog should work with the fixed event"() {
        when: "Creating a mock Transfer log"
        def transferLog = EventMockFactory.createMockTransferLog()

        then: "The log should be created successfully"
        transferLog != null
        transferLog.address == "0x88eEA3e4F0839B74A8dE27951bc630126837d646"
        transferLog.topics != null
        transferLog.topics.size() == 1
        transferLog.topics[0].startsWith("0x")
    }

    def "test Transfer event signature generation"() {
        when: "Creating a Transfer event and encoding it"
        def transferEvent = EventMockFactory.createMockTransferEvent()
        def eventSignature = EventEncoder.encode(transferEvent)

        then: "The signature should be generated correctly"
        eventSignature != null
        println("Transfer event signature: ${eventSignature}")

        and: "The signature should represent a tuple structure"
        // The signature should contain the tuple structure with 17 components
        // Format should be: Transfer((bytes32,uint16,bytes32,bytes32,uint256,uint256,uint256,uint16,bytes32,bytes32,string,bytes32,string,uint256,bytes32,string,string),bytes32)
        eventSignature.startsWith("0x")
    }
}
